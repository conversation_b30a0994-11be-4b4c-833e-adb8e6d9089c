import { DEFAULT_HEADERS } from './constants.service';
import http from './http';
import generateApiEndpoint from '@/helpers/generateApiEndpoint';

export const getCategoriesByOrganizationId = (organizationId) => {
  return http.get(generateApiEndpoint('human-handover/categories'), {
    params: {
      organization_id: organizationId,
    },
    headers: DEFAULT_HEADERS,
  });
};

export const createCategory = (data) => {
  return http.post(generateApiEndpoint('human-handover/categories'), data, {
    headers: DEFAULT_HEADERS,
  });
};

export const updateCategory = (category_id, data) => {
  return http.patch(
    generateApiEndpoint(`human-handover/categories/${category_id}`),
    data,
    {
      headers: DEFAULT_HEADERS,
    }
  );
};

export const deleteCategory = (category_id) => {
  return http.delete(generateApiEndpoint(`human-handover/categories/${category_id}`),
    {
      headers: DEFAULT_HEADERS,
    }
  );
};

export const getNotificationSettings = (organization_id) => {
  return http.get(generateApiEndpoint('human-handover/notification-settings'), {
    params: {
      organization_id: organization_id,
    },
    headers: DEFAULT_HEADERS,
  });
};

export const updateNotificationSettings = (organization_id, data) => {
  return http.patch(
    generateApiEndpoint('human-handover/notification-settings'),
    data,
    {
      params: {
        organization_id: organization_id,
      },
      headers: DEFAULT_HEADERS,
    }
  );
};

export const getQuickResponses = (organization_id) => {
  return http.get(generateApiEndpoint('human-handover/quick-responses'), {
    params: {
      organization_id: organization_id,
    },
    headers: DEFAULT_HEADERS,
  });
};

export const createQuickResponse = (data) => {
  return http.post(generateApiEndpoint('human-handover/quick-responses'), data, {
    headers: DEFAULT_HEADERS,
  });
};

export const updateQuickResponse = (quick_response_id, data) => {
  return http.patch(
    generateApiEndpoint(`human-handover/quick-responses/${quick_response_id}`),
    data,
    { headers: DEFAULT_HEADERS }
  );
};

export const deleteQuickResponse = (quick_response_id) => {
  return http.delete(
    generateApiEndpoint(`human-handover/quick-responses/${quick_response_id}`),
    { headers: DEFAULT_HEADERS }
  );
};

export const getInsights = (organization_id, date_from, date_to) => {
  return http.get(generateApiEndpoint(`human-handover/insights/${organization_id}`), {
    params: {
      date_created_from: date_from,
      date_created_to: date_to,
    },
    headers: DEFAULT_HEADERS,
  });
};

export const getPingDashboardData = (organization_id) => {
  return http.get(
    generateApiEndpoint(`human-handover/dashboard/${organization_id}/ping`),
    {
      headers: DEFAULT_HEADERS,
    }
  );
};

export const getConversationsDashboardData = (organization_id, params) => {
  return http.get(
    generateApiEndpoint(`human-handover/dashboard/${organization_id}`),
    {
      params,
      headers: DEFAULT_HEADERS,
    }
  );
};

export const getConversationById = ({ conversation_id }) => {
  return http.get(generateApiEndpoint(`human-handover/conversations/${conversation_id}`), {
    headers: DEFAULT_HEADERS,
  });
};

export const takeConversation = (conversation_id) => {
  return http.post(generateApiEndpoint(`human-handover/conversations/${conversation_id}/take`), {}, {
    headers: DEFAULT_HEADERS,
  }
  );
};

export const markResolved = (conversation_id) => {
  return http.post(
    generateApiEndpoint(`human-handover/conversations/${conversation_id}/resolve`),
    {},
    {
      headers: DEFAULT_HEADERS,
    });
};

export const getMessagesByConversationId = (conversation_id, token, timezone) => {
  return http.get(generateApiEndpoint(`human-handover/conversations/${conversation_id}/messages`), {
    params: {
      timezone,
    },
    headers: DEFAULT_HEADERS,
  });
};

export const updateCategoryConversation = (hh_conversation_id, hh_category_id) => {
  return http.patch(generateApiEndpoint(`human-handover/conversations/${hh_conversation_id}/update_category`), {}, {
    params: {
      hh_category_id,
    },
    headers: DEFAULT_HEADERS,
  });
};

export const getConversationMode = (conversation_id, token) => {
  return http.get(generateApiEndpoint(`human-handover/conversations/${conversation_id}/mode`), {
    params: {
      token,
    },
    headers: {
      ...DEFAULT_HEADERS,
    },
  });
};

export const getFileUploadSetting = (organization_id) => {
  return http.get(generateApiEndpoint('human-handover/notification-settings/allow-file-upload'), {
    params: {
      organization_id: organization_id,
    },
    headers: DEFAULT_HEADERS,
  });
};

export const updateFileUploadSetting = (organization_id, allow_file_upload) => {
  return http.patch(
    generateApiEndpoint('human-handover/notification-settings/allow-file-upload'),
    {},
    {
      params: {
        organization_id: organization_id,
        allow_file_upload: allow_file_upload,
      },
      headers: DEFAULT_HEADERS,
    }
  );
};

// Token-based version for shared/embedded chats
export const getFileUploadSettingByToken = (token) => {
  return http.get(
    generateApiEndpoint('human-handover/notification-settings/allow-file-upload'),
    {
      params: {
        token: token,
      },
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};